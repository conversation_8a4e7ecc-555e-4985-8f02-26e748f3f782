<script lang="ts" setup>
import { ArrowLeft02Icon, Calendar03Icon, Clock01Icon, UserIcon, Share08Icon } from 'hugeicons-vue'
import { getBlogPostBySlug, getRelatedPosts } from '~/data/blogPosts'

const route = useRoute()
const slug = route.params.slug as string

// Get blog post data from the data file
const blogPost = getBlogPostBySlug(slug)

// Handle case where blog post is not found
if (!blogPost) {
  throw createError({
    statusCode: 404,
    statusMessage: 'Blog post not found'
  })
}

// SEO and meta
useHead({
  title: `${blogPost.title} - TicketPie Blog`,
  meta: [
    { name: 'description', content: blogPost.excerpt },
    { property: 'og:title', content: blogPost.title },
    { property: 'og:description', content: blogPost.excerpt },
    { property: 'og:image', content: blogPost.featuredImage },
    { property: 'og:type', content: 'article' },
  ],
})

// Format date for display
const formattedDate = computed(() => {
  return blogPost.publishedAt.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  })
})

// Get category color
const categoryColor = computed(() => {
  const colors: Record<string, string> = {
    events: 'bg-blue-100 text-blue-800',
    tips: 'bg-green-100 text-green-800',
    updates: 'bg-purple-100 text-purple-800',
    guides: 'bg-orange-100 text-orange-800',
  }
  return colors[blogPost.category] || 'bg-slate-100 text-slate-800'
})

// Get related posts
const relatedPosts = getRelatedPosts(blogPost, 2)

function goBack() {
  navigateTo('/blog')
}

function sharePost() {
  if (navigator.share) {
    navigator.share({
      title: blogPost.value.title,
      text: blogPost.value.excerpt,
      url: window.location.href,
    })
  } else {
    // Fallback: copy URL to clipboard
    navigator.clipboard.writeText(window.location.href)
    // You could show a toast notification here
  }
}
</script>

<template>
  <div class="min-h-screen bg-pie-25">
    <!-- Back Button -->
    <div class="w-full px-4 pt-8">
      <div class="max-w-4xl mx-auto">
        <button
          class="flex items-center gap-2 text-pie-600 hover:text-pie-700 transition-colors duration-200 mb-6"
          @click="goBack"
        >
          <ArrowLeft02Icon class="w-5 h-5" />
          <span class="text-sm-medium">{{ $t('blog.back_to_blog') }}</span>
        </button>
      </div>
    </div>

    <!-- Article Header -->
    <article class="w-full px-4 pb-12">
      <div class="max-w-4xl mx-auto">
        <!-- Featured Image -->
        <div class="relative mb-8 rounded-2xl overflow-hidden">
          <img
            :src="blogPost.featuredImage"
            :alt="blogPost.title"
            class="w-full h-64 md:h-96 object-cover"
          >
          <!-- Category Badge -->
          <div class="absolute top-6 left-6">
            <span
              class="px-4 py-2 rounded-full text-sm font-medium"
              :class="categoryColor"
            >
              {{ $t(`blog.categories.${blogPost.category}`) }}
            </span>
          </div>
        </div>

        <!-- Article Meta -->
        <div class="bg-white rounded-2xl shadow-sm border border-slate-200 p-6 md:p-8 mb-8">
          <!-- Title -->
          <h1 class="text-3xl md:text-5xl font-sofia font-[800] text-pie-700 mb-6 leading-tight">
            {{ blogPost.title }}
          </h1>

          <!-- Meta Information -->
          <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-6">
            <div class="flex flex-wrap items-center gap-4 text-sm text-slate-600">
              <div class="flex items-center gap-2">
                <UserIcon class="w-4 h-4" />
                <span>{{ blogPost.author }}</span>
              </div>
              <div class="flex items-center gap-2">
                <Calendar03Icon class="w-4 h-4" />
                <span>{{ formattedDate }}</span>
              </div>
              <div class="flex items-center gap-2">
                <Clock01Icon class="w-4 h-4" />
                <span>{{ blogPost.readTime }} {{ $t('blog.read_time_minutes') }}</span>
              </div>
            </div>

            <!-- Share Button -->
            <button
              class="flex items-center gap-2 px-4 py-2 bg-pie-100 text-pie-700 rounded-lg hover:bg-pie-200 transition-colors duration-200"
              @click="sharePost"
            >
              <Share08Icon class="w-4 h-4" />
              <span class="text-sm-medium">{{ $t('blog.share') }}</span>
            </button>
          </div>

          <!-- Tags -->
          <div class="flex flex-wrap gap-2">
            <span
              v-for="tag in blogPost.tags"
              :key="tag"
              class="px-3 py-1 bg-slate-100 text-slate-700 rounded-full text-sm"
            >
              {{ tag }}
            </span>
          </div>
        </div>

        <!-- Article Content -->
        <div class="bg-white rounded-2xl shadow-sm border border-slate-200 p-6 md:p-8 mb-12">
          <div
            class="prose prose-lg max-w-none prose-headings:text-pie-700 prose-headings:font-sofia prose-headings:font-bold prose-p:text-slate-700 prose-p:leading-relaxed prose-ul:text-slate-700 prose-li:text-slate-700"
            v-html="blogPost.content"
          />
        </div>

        <!-- Related Posts -->
        <div v-if="relatedPosts.length > 0" class="bg-white rounded-2xl shadow-sm border border-slate-200 p-6 md:p-8">
          <h3 class="text-2xl font-sofia font-[700] text-pie-700 mb-6">
            {{ $t('blog.related_posts') }}
          </h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <NuxtLinkLocale
              v-for="post in relatedPosts"
              :key="post.id"
              :to="`/blog/${post.slug}`"
              class="group"
            >
              <div class="flex gap-4 p-4 rounded-lg border border-slate-200 hover:border-pie-300 hover:bg-pie-50 transition-all duration-200">
                <img
                  :src="post.featuredImage"
                  :alt="post.title"
                  class="w-20 h-20 object-cover rounded-lg flex-shrink-0"
                >
                <div class="flex-1">
                  <h4 class="text-base-bold text-slate-900 group-hover:text-pie-700 transition-colors duration-200 line-clamp-2 mb-2">
                    {{ post.title }}
                  </h4>
                  <span
                    class="px-2 py-1 rounded text-xs font-medium"
                    :class="categoryColor"
                  >
                    {{ $t(`blog.categories.${post.category}`) }}
                  </span>
                </div>
              </div>
            </NuxtLinkLocale>
          </div>
        </div>
      </div>
    </article>
  </div>
</template>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
